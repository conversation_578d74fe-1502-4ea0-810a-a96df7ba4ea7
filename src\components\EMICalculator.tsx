import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { formatCurrency, formatNumber } from "@/lib/utils";
import { parse, isValid, format } from "date-fns";

interface EMIResult {
  monthlyEMI: number;
  totalAmount: number;
  totalInterest: number;
  tenureMonths: number;
  tenureYears: number;
}

export function EMICalculator() {
  const [fromDateText, setFromDateText] = useState<string>("");
  const [loanDurationMonths, setLoanDurationMonths] = useState<string>("");
  const [loanAmount, setLoanAmount] = useState<string>("");
  const [interestRate, setInterestRate] = useState<string>("");
  const [result, setResult] = useState<EMIResult | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Smart date parser function
  const parseSmartDate = (dateText: string): Date | null => {
    if (!dateText.trim()) return null;

    const text = dateText.trim().toLowerCase();
    const currentYear = new Date().getFullYear();

    // Try different date formats
    const formats = [
      "dd-MM-yyyy",
      "dd/MM/yyyy",
      "dd.MM.yyyy",
      "yyyy-MM-dd",
      "MM/dd/yyyy",
      "dd-MM-yy",
      "dd/MM/yy",
    ];

    // Try parsing with different formats
    for (const formatStr of formats) {
      try {
        const parsed = parse(dateText, formatStr, new Date());
        if (isValid(parsed)) {
          return parsed;
        }
      } catch (e) {
        // Continue to next format
      }
    }

    // Try parsing natural language dates
    try {
      // Handle formats like "june 10 2025", "jan 15 2024", etc.
      const monthNames = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];

      const monthAbbr = [
        "jan",
        "feb",
        "mar",
        "apr",
        "may",
        "jun",
        "jul",
        "aug",
        "sep",
        "oct",
        "nov",
        "dec",
      ];

      // Match patterns like "june 10 2025" or "jan 15 2024"
      const naturalMatch = text.match(/(\w+)\s+(\d{1,2})\s+(\d{4})/);
      if (naturalMatch) {
        const [, monthStr, day, year] = naturalMatch;
        let monthIndex = monthNames.indexOf(monthStr);
        if (monthIndex === -1) {
          monthIndex = monthAbbr.indexOf(monthStr);
        }
        if (monthIndex !== -1) {
          const date = new Date(parseInt(year), monthIndex, parseInt(day));
          if (isValid(date)) {
            return date;
          }
        }
      }

      // Try native Date parsing as fallback
      const nativeDate = new Date(dateText);
      if (isValid(nativeDate) && nativeDate.getFullYear() > 1900) {
        return nativeDate;
      }
    } catch (e) {
      // Ignore parsing errors
    }

    return null;
  };

  const validateInputs = () => {
    const newErrors: Record<string, string> = {};

    // Validate from date
    if (!fromDateText.trim()) {
      newErrors.fromDate = "Start date is required";
    } else {
      const parsedDate = parseSmartDate(fromDateText);
      if (!parsedDate) {
        newErrors.fromDate =
          "Invalid date format. Try: 21-10-2025 or June 10 2025";
      }
    }

    // Validate loan duration
    if (!loanDurationMonths.trim()) {
      newErrors.loanDuration = "Loan duration is required";
    } else {
      const duration = parseInt(loanDurationMonths);
      if (isNaN(duration) || duration <= 0 || duration > 600) {
        newErrors.loanDuration = "Duration must be between 1 and 600 months";
      }
    }

    if (!loanAmount || parseFloat(loanAmount) <= 0) {
      newErrors.loanAmount = "Valid loan amount is required";
    }

    if (
      !interestRate ||
      parseFloat(interestRate) <= 0 ||
      parseFloat(interestRate) > 100
    ) {
      newErrors.interestRate = "Interest rate must be between 0 and 100";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateEMI = () => {
    if (!validateInputs()) return;

    const principal = parseFloat(loanAmount);
    const annualRate = parseFloat(interestRate);
    const monthlyRate = annualRate / (12 * 100);
    const tenureMonths = parseInt(loanDurationMonths);

    // EMI = [P x R x (1+R)^N]/[(1+R)^N-1]
    const emi =
      (principal * monthlyRate * Math.pow(1 + monthlyRate, tenureMonths)) /
      (Math.pow(1 + monthlyRate, tenureMonths) - 1);

    const totalAmount = emi * tenureMonths;
    const totalInterest = totalAmount - principal;

    setResult({
      monthlyEMI: emi,
      totalAmount,
      totalInterest,
      tenureMonths,
      tenureYears: Math.floor(tenureMonths / 12),
    });
  };

  const resetForm = () => {
    setFromDateText("");
    setLoanDurationMonths("");
    setLoanAmount("");
    setInterestRate("");
    setResult(null);
    setErrors({});
  };

  // Real-time calculation
  useEffect(() => {
    if (
      fromDateText &&
      loanDurationMonths &&
      loanAmount &&
      interestRate &&
      Object.keys(errors).length === 0
    ) {
      calculateEMI();
    }
  }, [fromDateText, loanDurationMonths, loanAmount, interestRate]);

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          EMI Calculator
        </CardTitle>
        <CardDescription className="text-center">
          Calculate your Equated Monthly Installment for loans
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fromDate">Start Date</Label>
            <Input
              id="fromDate"
              type="text"
              placeholder="e.g., 21-10-2025 or June 10 2025"
              value={fromDateText}
              onChange={(e) => setFromDateText(e.target.value)}
              className={errors.fromDate ? "border-red-500" : ""}
            />
            {errors.fromDate && (
              <p className="text-sm text-red-600">{errors.fromDate}</p>
            )}
            <p className="text-xs text-gray-500">
              Formats: 21-10-2025, June 10 2025, 2025-10-21
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="loanDuration">Loan Duration (Months)</Label>
            <Input
              id="loanDuration"
              type="number"
              placeholder="e.g., 240 (20 years)"
              value={loanDurationMonths}
              onChange={(e) => setLoanDurationMonths(e.target.value)}
              className={errors.loanDuration ? "border-red-500" : ""}
              min="1"
              max="600"
            />
            {errors.loanDuration && (
              <p className="text-sm text-red-600">{errors.loanDuration}</p>
            )}
            <p className="text-xs text-gray-500">
              Enter duration in months (1-600)
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="loanAmount">Loan Amount (₹)</Label>
            <Input
              id="loanAmount"
              type="number"
              placeholder="Enter loan amount"
              value={loanAmount}
              onChange={(e) => setLoanAmount(e.target.value)}
              className={errors.loanAmount ? "border-red-500" : ""}
            />
            {errors.loanAmount && (
              <p className="text-sm text-red-600">{errors.loanAmount}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="interestRate">Interest Rate (%)</Label>
            <Input
              id="interestRate"
              type="number"
              step="0.01"
              placeholder="Enter interest rate"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              className={errors.interestRate ? "border-destructive" : ""}
            />
            {errors.interestRate && (
              <p className="text-sm text-red-600">{errors.interestRate}</p>
            )}
          </div>
        </div>

        <div className="flex gap-4">
          <Button onClick={calculateEMI} className="flex-1">
            Calculate EMI
          </Button>
          <Button onClick={resetForm} variant="outline" className="flex-1">
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg space-y-3">
            <h3 className="text-lg font-semibold mb-3">Calculation Results</h3>

            {/* Show parsed start date */}
            {fromDateText && (
              <div className="mb-3 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                <div className="flex justify-between text-sm">
                  <span className="text-blue-700">Loan Start Date:</span>
                  <span className="font-medium text-blue-800">
                    {parseSmartDate(fromDateText)
                      ? format(parseSmartDate(fromDateText)!, "PPP")
                      : "Invalid date"}
                  </span>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Monthly EMI:</span>
                  <span className="font-semibold">
                    {formatCurrency(result.monthlyEMI)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Amount:</span>
                  <span className="font-semibold">
                    {formatCurrency(result.totalAmount)}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Interest:</span>
                  <span className="font-semibold">
                    {formatCurrency(result.totalInterest)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Loan Tenure:</span>
                  <span className="font-semibold">
                    {result.tenureMonths} months ({result.tenureYears} years)
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
