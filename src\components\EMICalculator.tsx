import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { DatePicker } from '@/components/ui/date-picker'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { differenceInMonths } from 'date-fns'

interface EMIResult {
  monthlyEMI: number
  totalAmount: number
  totalInterest: number
  tenureMonths: number
  tenureYears: number
}

export function EMICalculator() {
  const [fromDate, setFromDate] = useState<Date>()
  const [toDate, setToDate] = useState<Date>()
  const [loanAmount, setLoanAmount] = useState<string>('')
  const [interestRate, setInterestRate] = useState<string>('')
  const [result, setResult] = useState<EMIResult | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateInputs = () => {
    const newErrors: Record<string, string> = {}

    if (!fromDate) {
      newErrors.fromDate = 'From date is required'
    }

    if (!toDate) {
      newErrors.toDate = 'To date is required'
    }

    if (fromDate && toDate && fromDate >= toDate) {
      newErrors.toDate = 'To date must be after from date'
    }

    if (!loanAmount || parseFloat(loanAmount) <= 0) {
      newErrors.loanAmount = 'Valid loan amount is required'
    }

    if (!interestRate || parseFloat(interestRate) <= 0 || parseFloat(interestRate) > 100) {
      newErrors.interestRate = 'Interest rate must be between 0 and 100'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const calculateEMI = () => {
    if (!validateInputs()) return

    const principal = parseFloat(loanAmount)
    const annualRate = parseFloat(interestRate)
    const monthlyRate = annualRate / (12 * 100)
    const tenureMonths = differenceInMonths(toDate!, fromDate!)

    if (tenureMonths <= 0) {
      setErrors({ toDate: 'Invalid date range' })
      return
    }

    // EMI = [P x R x (1+R)^N]/[(1+R)^N-1]
    const emi = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenureMonths)) / 
                (Math.pow(1 + monthlyRate, tenureMonths) - 1)

    const totalAmount = emi * tenureMonths
    const totalInterest = totalAmount - principal

    setResult({
      monthlyEMI: emi,
      totalAmount,
      totalInterest,
      tenureMonths,
      tenureYears: Math.floor(tenureMonths / 12)
    })
  }

  const resetForm = () => {
    setFromDate(undefined)
    setToDate(undefined)
    setLoanAmount('')
    setInterestRate('')
    setResult(null)
    setErrors({})
  }

  // Real-time calculation
  useEffect(() => {
    if (fromDate && toDate && loanAmount && interestRate && Object.keys(errors).length === 0) {
      calculateEMI()
    }
  }, [fromDate, toDate, loanAmount, interestRate])

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">EMI Calculator</CardTitle>
        <CardDescription className="text-center">
          Calculate your Equated Monthly Installment for loans
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fromDate">From Date</Label>
            <DatePicker
              date={fromDate}
              onDateChange={setFromDate}
              placeholder="Select start date"
            />
            {errors.fromDate && (
              <p className="text-sm text-destructive">{errors.fromDate}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="toDate">To Date</Label>
            <DatePicker
              date={toDate}
              onDateChange={setToDate}
              placeholder="Select end date"
            />
            {errors.toDate && (
              <p className="text-sm text-destructive">{errors.toDate}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="loanAmount">Loan Amount (₹)</Label>
            <Input
              id="loanAmount"
              type="number"
              placeholder="Enter loan amount"
              value={loanAmount}
              onChange={(e) => setLoanAmount(e.target.value)}
              className={errors.loanAmount ? 'border-destructive' : ''}
            />
            {errors.loanAmount && (
              <p className="text-sm text-destructive">{errors.loanAmount}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="interestRate">Interest Rate (%)</Label>
            <Input
              id="interestRate"
              type="number"
              step="0.01"
              placeholder="Enter interest rate"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              className={errors.interestRate ? 'border-destructive' : ''}
            />
            {errors.interestRate && (
              <p className="text-sm text-destructive">{errors.interestRate}</p>
            )}
          </div>
        </div>

        <div className="flex gap-4">
          <Button onClick={calculateEMI} className="flex-1">
            Calculate EMI
          </Button>
          <Button onClick={resetForm} variant="outline" className="flex-1">
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-muted rounded-lg space-y-3">
            <h3 className="text-lg font-semibold mb-3">Calculation Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Monthly EMI:</span>
                  <span className="font-semibold">{formatCurrency(result.monthlyEMI)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Amount:</span>
                  <span className="font-semibold">{formatCurrency(result.totalAmount)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Interest:</span>
                  <span className="font-semibold">{formatCurrency(result.totalInterest)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Loan Tenure:</span>
                  <span className="font-semibold">
                    {result.tenureMonths} months ({result.tenureYears} years)
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
