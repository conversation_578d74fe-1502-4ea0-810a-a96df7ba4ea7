import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'

interface GSTResult {
  baseAmount: number
  gstAmount: number
  totalAmount: number
  gstRate: number
}

export function GSTCalculator() {
  const [baseAmount, setBaseAmount] = useState<string>('')
  const [result, setResult] = useState<GSTResult | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const GST_RATE = 18 // Fixed 18% GST rate

  const validateInputs = () => {
    const newErrors: Record<string, string> = {}

    if (!baseAmount || parseFloat(baseAmount) <= 0) {
      newErrors.baseAmount = 'Valid base amount is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const calculateGST = () => {
    if (!validateInputs()) return

    const base = parseFloat(baseAmount)
    const gstAmount = (base * GST_RATE) / 100
    const totalAmount = base + gstAmount

    setResult({
      baseAmount: base,
      gstAmount,
      totalAmount,
      gstRate: GST_RATE
    })
  }

  const resetForm = () => {
    setBaseAmount('')
    setResult(null)
    setErrors({})
  }

  // Real-time calculation
  useEffect(() => {
    if (baseAmount && Object.keys(errors).length === 0) {
      calculateGST()
    }
  }, [baseAmount])

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">GST Calculator</CardTitle>
        <CardDescription className="text-center">
          Calculate GST amount with fixed 18% rate
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="baseAmount">Base Amount (₹)</Label>
          <Input
            id="baseAmount"
            type="number"
            placeholder="Enter base amount"
            value={baseAmount}
            onChange={(e) => setBaseAmount(e.target.value)}
            className={errors.baseAmount ? 'border-destructive' : ''}
          />
          {errors.baseAmount && (
            <p className="text-sm text-destructive">{errors.baseAmount}</p>
          )}
        </div>

        <div className="bg-muted p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">GST Rate:</span>
            <span className="font-semibold text-lg">{GST_RATE}%</span>
          </div>
        </div>

        <div className="flex gap-4">
          <Button onClick={calculateGST} className="flex-1">
            Calculate GST
          </Button>
          <Button onClick={resetForm} variant="outline" className="flex-1">
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-muted rounded-lg space-y-4">
            <h3 className="text-lg font-semibold mb-3">Calculation Results</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2 border-b border-border">
                <span className="text-muted-foreground">Base Amount:</span>
                <span className="font-semibold text-lg">{formatCurrency(result.baseAmount)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2 border-b border-border">
                <span className="text-muted-foreground">GST ({result.gstRate}%):</span>
                <span className="font-semibold text-lg text-orange-600">{formatCurrency(result.gstAmount)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2 bg-primary/10 px-3 rounded">
                <span className="font-medium">Total Amount:</span>
                <span className="font-bold text-xl text-primary">{formatCurrency(result.totalAmount)}</span>
              </div>
            </div>

            <div className="mt-4 p-3 bg-background rounded border-l-4 border-l-primary">
              <p className="text-sm text-muted-foreground">
                <strong>Breakdown:</strong> {formatCurrency(result.baseAmount)} + {formatCurrency(result.gstAmount)} = {formatCurrency(result.totalAmount)}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
