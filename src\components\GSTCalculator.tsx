import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";

interface InclusiveGSTResult {
  totalAmount: number;
  processingFee: number;
  gstAmount: number;
}

interface ExclusiveGSTResult {
  baseAmount: number;
  gstAmount: number;
  totalAmount: number;
}

export function GSTCalculator() {
  // Section 1: Amount Includes GST (Reverse GST)
  const [inclusiveAmount, setInclusiveAmount] = useState<string>("");
  const [inclusiveResult, setInclusiveResult] =
    useState<InclusiveGSTResult | null>(null);

  // Section 2: Amount Excludes GST (Forward GST)
  const [exclusiveAmount, setExclusiveAmount] = useState<string>("");
  const [exclusiveResult, setExclusiveResult] =
    useState<ExclusiveGSTResult | null>(null);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const GST_RATE = 18; // Fixed 18% GST rate

  // Calculate Reverse GST (Amount includes GST)
  const calculateInclusiveGST = () => {
    if (!inclusiveAmount || parseFloat(inclusiveAmount) <= 0) {
      setErrors({ inclusive: "Valid amount is required" });
      return;
    }

    const total = parseFloat(inclusiveAmount);
    // Processing Fee = Amount × 100 ÷ 118
    const processingFee = (total * 100) / 118;
    // GST Amount = Total Amount - Processing Fee
    const gstAmount = total - processingFee;

    setInclusiveResult({
      totalAmount: total,
      processingFee: Number(processingFee.toFixed(5)),
      gstAmount: Number(gstAmount.toFixed(5)),
    });
    setErrors({});
  };

  // Calculate Forward GST (Amount excludes GST)
  const calculateExclusiveGST = () => {
    if (!exclusiveAmount || parseFloat(exclusiveAmount) <= 0) {
      setErrors({ exclusive: "Valid amount is required" });
      return;
    }

    const base = parseFloat(exclusiveAmount);
    // Total Amount = Base Amount × 118%
    const totalAmount = (base * 118) / 100;
    // GST Amount = Total Amount - Base Amount
    const gstAmount = totalAmount - base;

    setExclusiveResult({
      baseAmount: base,
      gstAmount: Number(gstAmount.toFixed(5)),
      totalAmount: Number(totalAmount.toFixed(5)),
    });
    setErrors({});
  };

  const resetForm = () => {
    setInclusiveAmount("");
    setExclusiveAmount("");
    setInclusiveResult(null);
    setExclusiveResult(null);
    setErrors({});
  };

  // Real-time calculations
  useEffect(() => {
    if (inclusiveAmount && Object.keys(errors).length === 0) {
      calculateInclusiveGST();
    }
  }, [inclusiveAmount]);

  useEffect(() => {
    if (exclusiveAmount && Object.keys(errors).length === 0) {
      calculateExclusiveGST();
    }
  }, [exclusiveAmount]);

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          GST Calculator
        </CardTitle>
        <CardDescription className="text-center">
          Calculate GST with 18% rate - Choose your calculation method
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* GST Rate Display */}
        <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400 text-center">
          <div className="flex justify-center items-center space-x-2">
            <span className="text-blue-700 font-medium">GST Rate:</span>
            <span className="font-bold text-xl text-blue-800">{GST_RATE}%</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Section 1: Amount Includes GST (Reverse GST) */}
          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
              <h3 className="font-semibold text-green-800 mb-2">
                Section 1: Amount Includes GST
              </h3>
              <p className="text-sm text-green-700">
                Calculate processing fee from GST-inclusive amount
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="inclusiveAmount">
                Total Amount (GST Inclusive) ₹
              </Label>
              <Input
                id="inclusiveAmount"
                type="number"
                placeholder="e.g., 1000"
                value={inclusiveAmount}
                onChange={(e) => setInclusiveAmount(e.target.value)}
                className={errors.inclusive ? "border-red-500" : ""}
                step="0.01"
              />
              {errors.inclusive && (
                <p className="text-sm text-red-600">{errors.inclusive}</p>
              )}
            </div>

            {inclusiveResult && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3">
                <h4 className="font-medium text-gray-800">Results:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Fee:</span>
                    <span className="font-semibold">
                      ₹{inclusiveResult.processingFee.toFixed(5)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GST Amount:</span>
                    <span className="font-semibold text-green-600">
                      ₹{inclusiveResult.gstAmount.toFixed(5)}
                    </span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">Total Amount:</span>
                    <span className="font-bold">
                      ₹{inclusiveResult.totalAmount.toFixed(5)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Section 2: Amount Excludes GST (Forward GST) */}
          <div className="space-y-4">
            <div className="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400">
              <h3 className="font-semibold text-orange-800 mb-2">
                Section 2: Amount Excludes GST
              </h3>
              <p className="text-sm text-orange-700">
                Calculate total amount from GST-exclusive base amount
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="exclusiveAmount">
                Base Amount (GST Exclusive) ₹
              </Label>
              <Input
                id="exclusiveAmount"
                type="number"
                placeholder="e.g., 847.45"
                value={exclusiveAmount}
                onChange={(e) => setExclusiveAmount(e.target.value)}
                className={errors.exclusive ? "border-red-500" : ""}
                step="0.01"
              />
              {errors.exclusive && (
                <p className="text-sm text-red-600">{errors.exclusive}</p>
              )}
            </div>

            {exclusiveResult && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3">
                <h4 className="font-medium text-gray-800">Results:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base Amount:</span>
                    <span className="font-semibold">
                      ₹{exclusiveResult.baseAmount.toFixed(5)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GST Amount:</span>
                    <span className="font-semibold text-orange-600">
                      ₹{exclusiveResult.gstAmount.toFixed(5)}
                    </span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">Total Amount:</span>
                    <span className="font-bold">
                      ₹{exclusiveResult.totalAmount.toFixed(5)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Reset Button */}
        <div className="flex justify-center">
          <Button onClick={resetForm} variant="outline" className="px-8">
            Reset All Calculations
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
